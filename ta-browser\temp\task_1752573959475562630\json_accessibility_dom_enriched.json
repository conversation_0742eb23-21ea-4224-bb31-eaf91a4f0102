{"role": "WebArea", "name": "Google", "children": [{"name": "About", "mmid": "6", "tag": "a", "is_clickable": true, "class": "MV3Tnb"}, {"name": "Store", "mmid": "7", "tag": "a", "is_clickable": true, "class": "MV3Tnb"}, {"name": "Gmail ", "mmid": "13", "tag": "a", "is_clickable": true, "class": "gb_X"}, {"name": "Search for Images ", "mmid": "15", "tag": "a", "is_clickable": true, "class": "gb_X"}, {"role": "button", "name": "Google apps", "mmid": "18", "tag": "a", "is_clickable": true, "class": "gb_B", "has_svg": true, "tabindex": "0"}, {"name": "Sign in", "mmid": "22", "tag": "a", "is_clickable": true, "class": "gb_A"}, {"role": "combobox", "name": "q", "expanded": true, "focused": true, "autocomplete": "both", "value": "test", "mmid": "62", "tag": "textarea", "aria-label": "Search", "id": "APjFqb", "class": "g<PERSON>yf tawebagent-ui-automation-highlight"}, {"role": "button", "name": " Clear", "mmid": "65", "tag": "div", "is_clickable": true, "class": "vOY7J M2vV3", "has_svg": true, "tabindex": "0"}, {"role": "button", "name": "Search by voice", "mmid": "69", "tag": "div", "is_clickable": true, "class": "XDyW0e", "has_svg": true, "tabindex": "0"}, {"role": "button", "name": "Search by image", "mmid": "71", "tag": "div", "is_clickable": true, "class": "nDcEnd", "has_svg": true, "tabindex": "0"}, {"role": "listbox", "name": "", "orientation": "vertical", "mmid": "89", "tag": "ul", "is_clickable": true, "class": "G43f7e", "has_svg": true, "description": "test\ntestosterone\ntest my internet speed\ntesting\ntesticular torsion\ntest internet speed\ntesticular cancer symptoms\ntestimonial\ntestosterone booster\nTestament\nBand", "additional_info": []}, {"role": "button", "name": "btnK", "mmid": "438", "tag": "input", "is_clickable": true, "class": "gNO89b", "tag_type": "submit", "aria-label": "Google Search", "tabindex": "0"}, {"role": "button", "name": "btnI", "mmid": "439", "tag": "input", "is_clickable": true, "class": "RNmpXc", "tag_type": "submit", "aria-label": "I'm Feeling Lucky"}, {"role": "button", "name": "Report inappropriate predictions", "mmid": "441", "tag": "div", "is_clickable": true, "class": "WzNHm mWcf0e", "tabindex": "0"}, {"role": "button", "name": "btnK", "mmid": "445", "tag": "input", "is_clickable": true, "class": "gNO89b", "tag_type": "submit", "aria-label": "Google Search", "tabindex": "0"}, {"role": "button", "name": "btnI", "mmid": "446", "tag": "input", "is_clickable": true, "class": "", "tag_type": "submit", "aria-label": "I'm Feeling Lucky", "id": "gbqfbb", "tabindex": "0"}, {"name": "Advertising", "mmid": "485", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"name": "Business", "mmid": "486", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"name": "How Search works", "mmid": "487", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"name": "Applying AI towards science and the environment", "mmid": "489", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"name": "Privacy", "mmid": "493", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"name": "Terms", "mmid": "494", "tag": "a", "is_clickable": true, "class": "pHiOh"}, {"role": "button", "name": "Settings", "mmid": "498", "tag": "div", "is_clickable": true, "class": "CcNe6e", "tabindex": "0"}]}