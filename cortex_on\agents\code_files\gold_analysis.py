#!/usr/bin/env python
# filename: gold_analysis.py

def calculate_moving_average(prices):
    """Calculate the 7-day moving average of gold prices."""
    return sum(prices) / len(prices)

def get_price_difference_percentage(current_price, average_price):
    """Calculate percentage difference between current price and average."""
    return ((current_price - average_price) / average_price) * 100

def get_recommendation(price_diff_percentage):
    """Determine buy recommendation based on price difference percentage."""
    if price_diff_percentage < -2:
        return "Strong Buy"
    elif -2 <= price_diff_percentage < -1:
        return "Moderate Buy"
    elif -1 <= price_diff_percentage <= 1:
        return "Hold"
    else:
        return "Wait for Better Price"

def main():
    # Sample data (in USD) - In a real application, this would come from an API or user input
    current_price = 1950.50
    historical_prices = [1945.75, 1940.25, 1955.00, 1960.50, 1948.75, 1942.25, 1938.00]
    
    # Calculate 7-day moving average
    moving_average = calculate_moving_average(historical_prices)
    
    # Calculate percentage difference
    price_diff_percentage = get_price_difference_percentage(current_price, moving_average)
    
    # Get recommendation
    recommendation = get_recommendation(price_diff_percentage)
    
    # Print formatted output
    print("\nGold Price Analysis")
    print("=" * 50)
    print(f"Current Gold Price: ${current_price:.2f}")
    print(f"7-Day Moving Average: ${moving_average:.2f}")
    print(f"Price Difference: {price_diff_percentage:.2f}%")
    print("-" * 50)
    print(f"Recommendation: {recommendation}")
    print("=" * 50)

if __name__ == "__main__":
    main()