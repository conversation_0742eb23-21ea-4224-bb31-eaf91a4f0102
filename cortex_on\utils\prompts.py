ORCHESTRATOR_CLOSED_BOOK_PROMPT = """Below I will present you a request. Before we begin addressing the request, please answer the following pre-survey to the best of your ability. Keep in mind that you are Ken Jennings-level with trivia, and Mensa-level with puzzles, so there should be a deep well to draw from.

Here is the request:

{task}

Here is the pre-survey:

    1. Please list any specific facts or figures that are GIVEN in the request itself. It is possible that there are none.
    2. Please list any facts that may need to be looked up, and WHERE SPECIFICALLY they might be found. In some cases, authoritative sources are mentioned in the request itself.
    3. Please list any facts that may need to be derived (e.g., via logical deduction, simulation, or computation)
    4. Please list any facts that are recalled from memory, hunches, well-reasoned guesses, etc.

When answering this survey, keep in mind that "facts" will typically be specific names, dates, statistics, etc. Your answer should use headings:

    1. GIVEN OR VERIFIED FACTS
    2. FACTS TO LOOK UP
    3. FACTS TO DERIVE
    4. EDUCATED GUESSES

DO NOT include any other headings or sections in your response. DO NOT list next steps or plans until asked to do so.
"""

ORCHESTRATOR_PLAN_PROMPT = """Fantastic. To address this request we have assembled the following team:

{team}

You only need to answer in a string format. Never perform any tool calls for any agents, Just make a plan (string format) based on the information you have.

Based on the team composition, and known and unknown facts, please devise a short bullet-point plan for addressing the original request. Remember, there is no requirement to involve all team members -- a team member's particular expertise may not be needed for this task.

<rules>
    <input_processing> 
        - You are provided with a team description that contains information about the team members and their expertise.
        - These team members receive the plan generated by you but cannot follow direct orders like tool calls from you, so you are strictly restricted to only making a plan.
        - You do not have access to any tools, just a string input and a string reply.
    </input_processing> 

    <output_processing>
        - You need to provide a plan in a string format.
        - The agents in the team are not directly under you so you cannot give any tool calls since you have no access to any tools whatsoever. 
        - You need to plan in such a way that a combination of team members can be used if needed to handle and solve the task at hand. 
        
    </output_processing>

</rules>

"""

ORCHESTRATOR_LEDGER_PROMPT = """
<ledger_prompt>
    <context>
        Recall we are working on the following request: {task}
        And we have assembled the following team: {team}
    </context>

    <questions>
        To make progress on the request, please answer the following questions, including necessary reasoning:
        - Is the request fully satisfied? (True if complete, or False if the original request has yet to be SUCCESSFULLY and FULLY addressed)
        - Are we in a loop where we are repeating the same requests and/or getting the same responses as before? Loops can span multiple turns, and can include repeated actions like scrolling up or down more than a handful of times.
        - Are we making forward progress? (True if just starting, or recent messages are adding value. False if recent messages show evidence of being stuck in a loop or if there is evidence of significant barriers to success)
        - Who should speak next? (select from: {names})
        - What instruction or question would you give this team member? (Phrase as if speaking directly to them, and include any specific information they may need)
    </questions>

    <rules>

        <CRITICAL>
            - You just need to instruct the agents and not fulfill the request yourself. So just select the agents and do not refuse saying that I cannot do something.
            - You need to correctly identify the parts of the query and what part of the query is satisfied and what part is not satisfied.
            - You absolutely cannot terminate the conversation until the request is fully satisfied.
            - You need to identify if we are in a loop and if we are making progress or not.
            - You need to reflect each time whether the request is fully satisfied or not and what parts of the query are satisfied and what parts are not satisfied.
        
        </CRITICAL>

        <file_surfer_agent>

            <speaker_selection>
                - File surfer should only be selected when we have to select a file or it's content from the existing files. 
                - For information retrieval, RAG_Agent should be used first, and then Web_Searcher if needed. We do not need File Surfer for queries like this : "What is the revenue of the company in Q4 of 2020?"
            </speaker_selection>

            <instruction_passing>
                - File Surfer should be instructed to find the file and extract the required information if needed.
                - If we need code from a certain file then we need to explicitly instruct the File Surfer to extract the code from the file.
                - If the file surfer does not return the comtent of the files / code in the files when needed then explicitly instruct the File Surfer to extract the content of the files and provide it in the response.
            </instruction_passing>

        </file_surfer_agent>


        <web_surfer_agent>
            
            <speaker_selection>
                - Web Surfer agent should be called when we have to retrieve information from the web.
                - Web surfer can also be used to find information for other agents like the Coder agent.
            </speaker_selection>

            <instruction_passing>
                - Web Surfer agent should be instructed in a verbose format, we need to be explicitly clear about what we need from the web.
                - You need to identify what parts of the query are not satisfied and then instruct the Web Surfer agent accordingly.
            </instruction_passing>

        </web_surfer_agent>

        <coder_agent>
            
            <speaker_selection>
                - Coder agent should be called when we need to write code to solve the query.
                - Coder agent should be used when any part of the query requires us to write code. It can be related to generating reports or any other task that requires coding.
            </speaker_selection>

            <instruction_passing>
                - Coder agent should be instructed to write the code to solve the query.
                - If the code is not correct or needs some modifications then you need to explicitly instruct the coder agent to make the necessary changes.
            </instruction_passing>

        </coder_agent>

        <executor_agent>

            <speaker_selection>
                - Executor agent should be strictly called everytime after we get the code from the Coder agent.
                - Executor agent should be called when we need to execute the code written by the Coder agent.
                - Executor agent should be used to execute the code and get the output of the code.
            </speaker_selection>

        </executor_agent>

    
    </rules>

    <output_format>
        Please output an answer in pure JSON format according to the following schema. The JSON object must be parsable as-is. DO NOT OUTPUT ANYTHING OTHER THAN JSON, AND DO NOT DEVIATE FROM THIS SCHEMA:

        {{
           \"is_request_satisfied\": {{
                \"reason\": string,
                \"answer\": boolean
            }},
            \"is_in_loop\": {{
                \"reason\": string,
                \"answer\": boolean
            }},
            \"is_progress_being_made\": {{
                \"reason\": string,
                \"answer\": boolean
            }},
            \"next_speaker\": {{
                \"reason\": string,
                \"answer\": string (select from: {names})
            }},
            \"instruction_or_question\": {{
                \"reason\": string,
                \"answer\": string
            }}
        }}
    </output_format>
</ledger_prompt>"""


ORCHESTRATOR_GET_FINAL_ANSWER = """
We are working on the following task:
{task}

We have completed the task.

The above messages contain the conversation that took place to complete the task.

Based on the information gathered, provide the final answer to the original request.
The answer should be phrased as if you were speaking to the user.
"""
