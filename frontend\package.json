{"name": "cortexon-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.0.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.479.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-syntax-highlighter": "^15.6.1", "react-use-websocket": "^4.13.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.12", "tailwindcss-animate": "^1.0.7", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}