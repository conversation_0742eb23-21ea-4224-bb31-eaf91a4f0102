{"role": "WebArea", "name": "Google", "children": [{"role": "link", "name": "About", "keyshortcuts": "6"}, {"role": "link", "name": "Store", "keyshortcuts": "7"}, {"role": "link", "name": "Gmail ", "keyshortcuts": "13"}, {"role": "link", "name": "Search for Images ", "keyshortcuts": "15"}, {"role": "button", "name": "Google apps", "keyshortcuts": "18"}, {"role": "link", "name": "Sign in", "keyshortcuts": "22"}, {"role": "combobox", "name": "Search", "description": "Search", "keyshortcuts": "62", "expanded": true, "focused": true, "autocomplete": "both", "haspopup": "listbox", "value": "test"}, {"role": "button", "name": " Clear", "keyshortcuts": "65"}, {"role": "button", "name": "Search by voice", "keyshortcuts": "69"}, {"role": "button", "name": "Search by image", "keyshortcuts": "71"}, {"role": "listbox", "name": "", "keyshortcuts": "89", "orientation": "vertical"}, {"role": "button", "name": "Google Search", "description": "Google Search", "keyshortcuts": "438"}, {"role": "button", "name": "I'm Feeling Lucky", "description": "I'm Feeling Lucky", "keyshortcuts": "439"}, {"role": "button", "name": "Report inappropriate predictions", "keyshortcuts": "441"}, {"role": "button", "name": "Google Search", "description": "Google Search", "keyshortcuts": "445"}, {"role": "button", "name": "I'm Feeling Lucky", "description": "I'm Feeling Lucky", "keyshortcuts": "446"}, {"role": "link", "name": "Advertising", "keyshortcuts": "485"}, {"role": "link", "name": "Business", "keyshortcuts": "486"}, {"role": "link", "name": "How Search works", "keyshortcuts": "487"}, {"role": "link", "name": "Applying AI towards science and the environment", "keyshortcuts": "489"}, {"role": "link", "name": "Privacy", "keyshortcuts": "493"}, {"role": "link", "name": "Terms", "keyshortcuts": "494"}, {"role": "button", "name": "Settings", "keyshortcuts": "498", "haspopup": "menu"}]}