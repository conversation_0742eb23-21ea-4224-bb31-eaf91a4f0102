<svg width="221" height="55" viewBox="0 0 221 55" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M56.96 42.504C54.076 42.504 51.584 41.874 49.484 40.614C47.412 39.326 45.802 37.548 44.654 35.28C43.534 32.984 42.974 30.324 42.974 27.3C42.974 24.304 43.534 21.658 44.654 19.362C45.802 17.066 47.412 15.288 49.484 14.028C51.584 12.74 54.076 12.096 56.96 12.096C60.404 12.096 63.19 12.922 65.318 14.574C67.474 16.226 68.846 18.55 69.434 21.546H64.814C64.394 19.81 63.526 18.424 62.21 17.388C60.922 16.352 59.172 15.834 56.96 15.834C54.972 15.834 53.25 16.296 51.794 17.22C50.338 18.144 49.218 19.46 48.434 21.168C47.65 22.876 47.258 24.92 47.258 27.3C47.258 29.68 47.65 31.738 48.434 33.474C49.218 35.182 50.338 36.498 51.794 37.422C53.25 38.318 54.972 38.766 56.96 38.766C59.172 38.766 60.922 38.276 62.21 37.296C63.526 36.288 64.394 34.93 64.814 33.222H69.434C68.846 36.134 67.474 38.416 65.318 40.068C63.19 41.692 60.404 42.504 56.96 42.504ZM80.3806 42.504C78.4206 42.504 76.6426 42.056 75.0466 41.16C73.4786 40.236 72.2326 38.962 71.3086 37.338C70.3846 35.686 69.9226 33.768 69.9226 31.584C69.9226 29.4 70.3846 27.496 71.3086 25.872C72.2606 24.22 73.5206 22.946 75.0886 22.05C76.6846 21.126 78.4766 20.664 80.4646 20.664C82.4526 20.664 84.2306 21.126 85.7986 22.05C87.3946 22.946 88.6406 24.22 89.5366 25.872C90.4606 27.496 90.9226 29.4 90.9226 31.584C90.9226 33.768 90.4606 35.686 89.5366 37.338C88.6126 38.962 87.3526 40.236 85.7566 41.16C84.1606 42.056 82.3686 42.504 80.3806 42.504ZM80.3806 39.144C81.5006 39.144 82.5226 38.864 83.4466 38.304C84.3986 37.744 85.1686 36.918 85.7566 35.826C86.3446 34.706 86.6386 33.292 86.6386 31.584C86.6386 29.876 86.3446 28.462 85.7566 27.342C85.1966 26.222 84.4406 25.396 83.4886 24.864C82.5646 24.304 81.5566 24.024 80.4646 24.024C79.3726 24.024 78.3506 24.304 77.3986 24.864C76.4466 25.396 75.6766 26.222 75.0886 27.342C74.5006 28.462 74.2066 29.876 74.2066 31.584C74.2066 33.292 74.5006 34.706 75.0886 35.826C75.6766 36.918 76.4326 37.744 77.3566 38.304C78.3086 38.864 79.3166 39.144 80.3806 39.144ZM93.1752 42V21.168H96.8292L97.2072 25.704H97.3752C98.0472 24.22 98.7192 23.128 99.3912 22.428C100.091 21.7 100.889 21.224 101.785 21C102.709 20.776 103.801 20.664 105.061 20.664V25.074H103.969C102.877 25.074 101.911 25.214 101.071 25.494C100.259 25.746 99.5732 26.138 99.0132 26.67C98.4812 27.202 98.0752 27.888 97.7952 28.728C97.5152 29.54 97.3752 30.506 97.3752 31.626V42H93.1752ZM116.184 42C114.868 42 113.706 41.79 112.698 41.37C111.718 40.95 110.962 40.25 110.43 39.27C109.926 38.29 109.674 36.96 109.674 35.28V24.318H106.062V21.168H109.674L110.136 15.96H113.874V21.168H119.796V24.318H113.874V35.322C113.874 36.638 114.112 37.52 114.588 37.968C115.092 38.416 115.96 38.64 117.192 38.64H119.544V42H116.184ZM131.288 42.504C129.244 42.504 127.438 42.056 125.87 41.16C124.302 40.236 123.07 38.962 122.174 37.338C121.278 35.714 120.83 33.81 120.83 31.626C120.83 29.442 121.264 27.538 122.132 25.914C123.028 24.262 124.26 22.974 125.828 22.05C127.424 21.126 129.258 20.664 131.33 20.664C133.402 20.664 135.18 21.126 136.664 22.05C138.148 22.946 139.296 24.136 140.108 25.62C140.948 27.104 141.368 28.756 141.368 30.576C141.368 30.856 141.368 31.164 141.368 31.5C141.368 31.836 141.34 32.186 141.284 32.55H123.896V29.61H137.168C137.084 27.818 136.496 26.432 135.404 25.452C134.312 24.444 132.94 23.94 131.288 23.94C130.168 23.94 129.118 24.192 128.138 24.696C127.186 25.2 126.416 25.956 125.828 26.964C125.24 27.972 124.946 29.246 124.946 30.786V31.962C124.946 33.502 125.24 34.804 125.828 35.868C126.416 36.904 127.186 37.688 128.138 38.22C129.118 38.752 130.154 39.018 131.246 39.018C132.59 39.018 133.696 38.724 134.564 38.136C135.46 37.52 136.118 36.694 136.538 35.658H140.78C140.388 36.974 139.758 38.15 138.89 39.186C138.022 40.194 136.944 41.006 135.656 41.622C134.396 42.21 132.94 42.504 131.288 42.504ZM141.273 42L148.581 31.416L141.273 20.832H145.809L151.353 29.064L156.939 20.832H161.433L154.125 31.416L161.433 42H156.939L151.353 33.726L145.809 42H141.273ZM176.198 42.504C173.342 42.504 170.822 41.86 168.638 40.572C166.454 39.284 164.732 37.506 163.472 35.238C162.24 32.942 161.624 30.296 161.624 27.3C161.624 24.304 162.24 21.672 163.472 19.404C164.732 17.108 166.454 15.316 168.638 14.028C170.822 12.74 173.342 12.096 176.198 12.096C179.082 12.096 181.616 12.74 183.8 14.028C186.012 15.316 187.72 17.108 188.924 19.404C190.156 21.672 190.772 24.304 190.772 27.3C190.772 30.296 190.156 32.942 188.924 35.238C187.72 37.506 186.012 39.284 183.8 40.572C181.616 41.86 179.082 42.504 176.198 42.504ZM176.198 37.212C177.962 37.212 179.488 36.806 180.776 35.994C182.064 35.182 183.058 34.048 183.758 32.592C184.458 31.108 184.808 29.344 184.808 27.3C184.808 25.256 184.458 23.506 183.758 22.05C183.058 20.566 182.064 19.418 180.776 18.606C179.488 17.794 177.962 17.388 176.198 17.388C174.462 17.388 172.95 17.794 171.662 18.606C170.402 19.418 169.408 20.566 168.68 22.05C167.98 23.506 167.63 25.256 167.63 27.3C167.63 29.344 167.98 31.108 168.68 32.592C169.408 34.048 170.402 35.182 171.662 35.994C172.95 36.806 174.462 37.212 176.198 37.212ZM193.046 42V12.6H198.926L212.24 32.55V12.6H218.12V42H212.24L198.926 22.092V42H193.046Z" fill="white"/>
<g filter="url(#filter0_ii_3003_7944)">
<path d="M16.9132 11.4748C17.4528 10.7426 18.5472 10.7426 19.0868 11.4748L25.5154 20.1986C25.5959 20.3078 25.6922 20.4041 25.8014 20.4846L34.5252 26.9132C35.2574 27.4528 35.2574 28.5472 34.5252 29.0868L25.8014 35.5154C25.6922 35.5959 25.5959 35.6922 25.5154 35.8014L19.0868 44.5252C18.5472 45.2574 17.4528 45.2574 16.9132 44.5252L10.4846 35.8014C10.4041 35.6922 10.3078 35.5959 10.1986 35.5154L1.4748 29.0868C0.74262 28.5472 0.74262 27.4528 1.4748 26.9132L10.1986 20.4846C10.3078 20.4041 10.4041 20.3078 10.4846 20.1986L16.9132 11.4748Z" fill="url(#paint0_linear_3003_7944)"/>
</g>
<g filter="url(#filter1_ii_3003_7944)">
<path d="M16.8514 17.8594C17.379 17.0053 18.621 17.0053 19.1486 17.8594L22.4144 23.1465C22.5246 23.325 22.675 23.4754 22.8535 23.5856L28.1406 26.8514C28.9947 27.379 28.9947 28.621 28.1406 29.1486L22.8535 32.4144C22.675 32.5246 22.5246 32.675 22.4144 32.8535L19.1486 38.1406C18.621 38.9947 17.379 38.9947 16.8514 38.1406L13.5856 32.8535C13.4754 32.675 13.325 32.5246 13.1465 32.4144L7.8594 29.1486C7.00535 28.621 7.00535 27.379 7.85941 26.8514L13.1465 23.5856C13.325 23.4754 13.4754 23.325 13.5856 23.1465L16.8514 17.8594Z" fill="url(#paint1_linear_3003_7944)"/>
</g>
<g filter="url(#filter2_i_3003_7944)">
<path d="M17.8298 22.2755C17.908 22.1489 18.092 22.1489 18.1702 22.2755L20.2662 25.6688C20.2825 25.6952 20.3048 25.7175 20.3312 25.7338L23.7245 27.8298C23.8511 27.908 23.8511 28.092 23.7245 28.1702L20.3312 30.2662C20.3048 30.2825 20.2825 30.3048 20.2662 30.3312L18.1702 33.7245C18.092 33.8511 17.908 33.8511 17.8298 33.7245L15.7338 30.3312C15.7175 30.3048 15.6952 30.2825 15.6688 30.2662L12.2755 28.1702C12.1489 28.092 12.1489 27.908 12.2755 27.8298L15.6688 25.7338C15.6952 25.7175 15.7175 25.6952 15.7338 25.6688L17.8298 22.2755Z" fill="url(#paint2_linear_3003_7944)"/>
</g>
<defs>
<filter id="filter0_ii_3003_7944" x="0.925659" y="10.9257" width="34.1487" height="38.1487" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.0852539 0 0 0 0 0.740822 0 0 0 0.17 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3003_7944"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.573335 0 0 0 0 0.390479 0 0 0 0 1 0 0 0 0.54 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3003_7944" result="effect2_innerShadow_3003_7944"/>
</filter>
<filter id="filter1_ii_3003_7944" x="7.21887" y="17.2189" width="21.5623" height="33.5623" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.0852539 0 0 0 0 0.740822 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3003_7944"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.573335 0 0 0 0 0.390479 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3003_7944" result="effect2_innerShadow_3003_7944"/>
</filter>
<filter id="filter2_i_3003_7944" x="12.1806" y="22.1806" width="11.6389" height="11.6389" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3003_7944"/>
</filter>
<linearGradient id="paint0_linear_3003_7944" x1="4" y1="12.5" x2="32.5" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="#D2B6FF"/>
<stop offset="1" stop-color="#EEEEFF"/>
</linearGradient>
<linearGradient id="paint1_linear_3003_7944" x1="6" y1="16" x2="30" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#4E00CB"/>
<stop offset="1" stop-color="#4A4AFF"/>
</linearGradient>
<linearGradient id="paint2_linear_3003_7944" x1="15.5" y1="25" x2="22.5" y2="35" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5FCFF"/>
<stop offset="1" stop-color="#8C8CFF"/>
</linearGradient>
</defs>
</svg>
