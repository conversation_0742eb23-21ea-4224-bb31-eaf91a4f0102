version: '3.8'

services:
  cortex_on:
    build:
      context: ./cortex_on
      dockerfile: Dockerfile
    volumes:
      - ./cortex_on:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8081:8081"

  agentic_browser:
    build:
      context: ./ta-browser
      dockerfile: Dockerfile
    volumes:
      - ./ta-browser:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8000:8000"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
    env_file:
      - .env
    depends_on:
      - cortex_on
      - agentic_browser
    restart: always
    ports:
      - "3000:3000"
